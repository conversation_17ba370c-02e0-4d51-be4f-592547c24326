from django.contrib import admin

from organizational_management.charge.models import MaternityCostInfo, Package


# Register your models here.
@admin.register(Package)
class PackageAdmin(admin.ModelAdmin):
    list_display = ['name', 'price', 'description']

@admin.register(MaternityCostInfo)
class MaternityCostInfoAdmin(admin.ModelAdmin):
    list_display = ['maternity_admission', 'package', 'deposit_amount', 'earnest_amount', 'payable_amount', 'paid_amount', 'remaining_amount', 'payment_method', 'remark', 'payment_status']