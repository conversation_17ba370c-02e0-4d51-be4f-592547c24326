from datetime import datetime, timedelta

import jwt
from django.conf import settings
from rest_framework import authentication
from rest_framework import status
from rest_framework.exceptions import AuthenticationFailed
from rest_framework.permissions import BasePermission
from rest_framework.response import Response
from rest_framework.views import exception_handler

from core.resp import make_response
from user.models import Maternity, Staff
from user.serializers import MaternitySerializer, StaffSerializer


# 创建用户token
def create_user_token(user_id, user_type='maternity'):
    iat = datetime.utcnow()
    exp = iat + timedelta(weeks=48)
    return jwt.encode({
        'exp': exp,
        'iat': iat,
        'uid': user_id,
        'u_type': user_type,
    }, settings.SECRET_KEY)

# 认证失败
class TokenExpiredError(AuthenticationFailed):
    def __init__(self):
        super().__init__('Token has expired', code='token_expired')

# Token缺失异常
class TokenMissingError(AuthenticationFailed):
    def __init__(self):
        super().__init__('无效的登录凭据', code='token_missing')

# 认证
class CareCenterAuthentication(authentication.BaseAuthentication):
    def authenticate(self, request):
        if request.method == 'OPTIONS':
            return None
        print(f"DEBUG AUTH: Request method: {request.method}")
        return self.auth(request)

    @staticmethod
    def auth(request):
        token = request.headers.get('Authorization')
        if not token:
            raise TokenMissingError()
        if token.startswith('Bearer '):
            token = token.split(' ')[1]
        try:
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=['HS256'])
        except Exception as e:
            raise TokenExpiredError()
        user_type = payload.get('u_type', 'maternity')
        user_model, user_serializer = get_user_model(user_type)
        try:
            user = user_model.objects.get(uid=payload.get('uid'))
            auth_data = {
                'user': user_serializer(user).data,
                'user_type': user_type,
            }
            print(f"DEBUG AUTH: Authenticated User ID: {user.uid}, Type: {user_type}")
            return user, auth_data
        except user_model.DoesNotExist:
            raise TokenExpiredError()


# 异常处理
def care_center_exception_handler(exc, context):
    if isinstance(exc, TokenExpiredError):
        return make_response(code=403, msg='您的登录已过期，请重新登录')
    if isinstance(exc, TokenMissingError):
        return make_response(code=403, msg='无效的身份凭据')
    
    # 增加对ValidationError的特殊处理
    from rest_framework.exceptions import ValidationError
    if isinstance(exc, ValidationError):
        return make_response(code=400, msg=exc.detail, data=exc.detail)
        
    if not hasattr(exc, 'status_code'):
        response = exception_handler(exc, context)
        return response
    if exc.status_code == 401:
        return Response({
            'code': exc.status_code,
            'msg': exc.default_detail
        }, status=status.HTTP_401_UNAUTHORIZED)
    return Response({
        'code': exc.status_code,
        'msg': exc.default_detail,
    }, status=status.HTTP_200_OK)

# 获取用户模型
def get_user_model(user_type):
    if user_type == 'maternity':
        return Maternity , MaternitySerializer
    else:
        return Staff , StaffSerializer



# 普通权限
class HasPermission(BasePermission):
    def has_permission(self, request, view):
        return isinstance(request.user, Maternity) or isinstance(request.user, Staff)
    
#判断 staff 用户
# TODO: 需要细分权限
class HasStaffPermission(BasePermission):
        
    """
    判断 staff 用户
    1. 如果用户是 staff 用户，直接允许访问
    """
    
    def has_permission(self, request, view):
        return isinstance(request.user, Staff)
    
#判断 maternity 用户
class HasMaternityPermission(BasePermission):
    
    """
    判断 maternity 用户
    1. 如果用户是 maternity 用户，直接允许访问
    """
    
    def has_permission(self, request, view):
        print(f"DEBUG HMP: Checking permission for User: {request.user} (Type: {type(request.user)}) on View: {view.__class__.__name__}")
        return isinstance(request.user, Maternity)
    
    

# 产妇可直接访问，员工需要特定权限
class MaternityOrStaffWithPermission(BasePermission):
    """
    混合权限验证：
    1. 如果用户是产妇，直接允许访问
    2. 如果用户是员工，则需要验证特定权限

    使用方法：
    1. 在视图类中设置类属性 staff_required_permission (支持单个权限或权限列表)
    2. 或在 get_staff_required_permission 方法中返回所需权限码
    """

    def has_permission(self, request, view):

        user = request.user
        print(f"DEBUG MOSP: Checking permission for User: {user} (Type: {type(user)}) on View: {view.__class__.__name__}")
        # 如果是产妇用户，直接允许访问
        if isinstance(user, Maternity):
            return True

        # 如果不是员工用户，拒绝访问
        if not isinstance(user, Staff):

            return False

        # 获取权限码 - 先查找类属性，再查找方法
        permission = getattr(view, 'staff_required_permission', None)
        if permission is None and hasattr(view, 'get_staff_required_permission'):
            permission = view.get_staff_required_permission(request)
        print(f"DEBUG MOSP: Required permission: {permission}")
        # 如果没有指定权限码，默认拒绝访问
        if not permission:
            return False

        # 验证员工是否拥有指定权限
        # 支持单个权限或权限列表
        if isinstance(permission, (list, tuple)):
            # 如果是权限列表，用户只需拥有其中任意一个权限即可
            return any(user.has_permission(perm) for perm in permission)
        else:
            # 单个权限验证
            return user.has_permission(permission)
    
    
# 仅员工且有特定权限可访问
class StaffWithSpecificPermissionOnly(BasePermission):
    """
    严格权限验证：
    1. 产妇用户无法访问
    2. 员工用户需要验证特定权限才能访问

    使用方法：
    1. 在视图类中设置类属性 staff_required_permission (支持单个权限或权限列表)
    2. 或在 get_staff_required_permission 方法中返回所需权限码
    """

    def has_permission(self, request, view):
        user = request.user
        print(f"DEBUG: StaffWithSpecificPermissionOnly - User: {user}, Type: {type(user)}")

        # 如果是产妇用户，直接拒绝访问
        if isinstance(user, Maternity):
            return False

        # 如果不是员工用户，拒绝访问
        if not isinstance(user, Staff):
            return False

        # 获取权限码 - 先查找类属性，再查找方法
        permission = getattr(view, 'staff_required_permission', None)
        if permission is None and hasattr(view, 'get_staff_required_permission'):
            permission = view.get_staff_required_permission(request)

        # 如果没有指定权限码，默认拒绝访问
        if not permission:
            return False

        # 验证员工是否拥有指定权限
        # 支持单个权限或权限列表
        if isinstance(permission, (list, tuple)):
            # 如果是权限列表，用户只需拥有其中任意一个权限即可
            return any(user.has_permission(perm) for perm in permission)
        else:
            # 单个权限验证
            return user.has_permission(permission)