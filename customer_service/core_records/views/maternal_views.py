from rest_framework.views import APIView

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.resp import make_response
from customer_service.core_records.models.maternal import MaternityDailyPhysicalCareRecord, \
    MaternityDailyRequiredRecord, MaternityDailyDietRecord, MaternityCheckInAssessment, \
    MaternityRehabilitationAssessmentRecord
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.core_records.serializers.maternal import MaternityCheckInAssessmentDetailSerializer, \
    MaternityDailyPhysicalCareRecordCreateSerializer, \
    MaternityDailyPhysicalCareRecordDetailSerializer, MaternityDailyPhysicalCareRecordListSerializer, \
    MaternityDailyPhysicalCareRecordUpdateSerializer, MaternityDailyRequiredRecordCreateSerializer, \
    MaternityDailyRequiredRecordDetailSerializer, MaternityDailyRequiredRecordListSerializer, \
    MaternityDailyRequiredRecordUpdateSerializer, \
    MaternityDietRecordCreateSerializer, MaternityDietRecordDetailSerializer, MaternityDietRecordListSerializer, \
    MaternityCheckInAssessmentCreateSerializer, MaternityCheckInAssessmentUpdateSerializer, \
    MaternityDietRecordUpdateSerializer, MaternityRehabilitationAssessmentRecordCreateSerializer, \
    MaternityRehabilitationAssessmentRecordDetailSerializer, MaternityRehabilitationAssessmentRecordListSerializer, \
    MaternityRehabilitationAssessmentRecordUpdateSerializer
from permissions.enum import PermissionEnum


# 产妇记录列表基类
class MaternityRecordListBaseView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW

    model_class = None  
    serializer_class = None  
    success_message = "" 

    def get_base_queryset(self, aid):
 
        if not self.model_class:
            raise NotImplementedError("数据不存在")

        return self.model_class.objects.filter(
            maternity_admission__aid=aid,
            maternity_admission__maternity_center=self.request.user.maternity_center
        )

    def apply_additional_filters(self, queryset):
        return queryset

    def get_ordering(self):
        return ['-created_at']

    def paginate_queryset(self, queryset):
        try:
            page = int(self.request.query_params.get('page', 1))
            page_size = int(self.request.query_params.get('page_size', 10))
        except ValueError:
            page = 1
            page_size = 10

        page = max(1, page)
        page_size = min(max(1, page_size), 100) 

        total_count = queryset.count()
        total_page = (total_count + page_size - 1) // page_size
        start = (page - 1) * page_size
        end = start + page_size

        paginated_data = queryset[start:end]

        return {
            'data': paginated_data,
            'pagination': {
                'page': page,
                'page_size': page_size,
                'total_count': total_count,
                'total_page': total_page
            }
        }

    def get(self, request, aid):

        try:
            queryset = self.get_base_queryset(aid)

            queryset = self.apply_additional_filters(queryset)

            ordering = self.get_ordering()
            if ordering:
                queryset = queryset.order_by(*ordering)

            paginated_result = self.paginate_queryset(queryset)

            if not self.serializer_class:
                raise NotImplementedError("数据类型不存在")

            serializer = self.serializer_class(paginated_result['data'], many=True)

            result = {
                'list': serializer.data,
                **paginated_result['pagination']
            }

            return make_response(
                code=0,
                msg=self.success_message or "获取列表成功",
                data=result
            )

        except Exception as e:
            return make_response(code=-1, msg=f"获取列表失败: {str(e)}")



# 产妇每日必填记录列表
class MaternityDailyRequiredRecordListView(MaternityRecordListBaseView):

    model_class = MaternityDailyRequiredRecord
    serializer_class = MaternityDailyRequiredRecordListSerializer
    success_message = "获取产妇每日必填记录列表成功"
    
    
# 产妇每日必填记录详情
class MaternityDailyRequiredRecordDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    
    def get(self, request, record_id):
        try:
            instance = MaternityDailyRequiredRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityDailyRequiredRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        serializer = MaternityDailyRequiredRecordDetailSerializer(instance)
        return make_response(code=0, msg="获取产妇每日必填记录详情成功", data=serializer.data)
    
    

# 创建产妇每日必填记录
class MaternityDailyRequiredRecordCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    
    def post(self, request, aid):
        
        data = request.data.copy()
        
        amins = MaternityAdmission.get_maternity_admission_by_aid(aid,request.user.maternity_center)
        if not amins:
            return make_response(code=-1, msg="产妇入院记录不存在")
        
        if MaternityDailyRequiredRecord.check_record_date_exists(amins.id,data['record_date']):
            return make_response(code=-1, msg=f"{amins.maternity.name}（{data['record_date']}）已有每日必填记录，不允许创建新的记录")
        
        data['creator'] = request.user.id
        data['maternity_admission'] = amins.id
        
        serializer = MaternityDailyRequiredRecordCreateSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="创建产妇每日必填记录成功", data=MaternityDailyRequiredRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="创建产妇每日必填记录失败", data=serializer.errors)


# 更新产妇每日必填记录
class MaternityDailyRequiredRecordUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    
    def put(self, request, record_id):
        
        data = request.data.copy()
        
        try:
            instance = MaternityDailyRequiredRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityDailyRequiredRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        record_date = data.get('record_date')
        
        if record_date:
            if MaternityDailyRequiredRecord.check_record_date_exists(instance.maternity_admission.id,record_date,instance.id):
                return make_response(code=-1, msg=f"{instance.maternity_admission.maternity.name}（{record_date}）已有每日必填记录，请选择其他日期")
        
        serializer = MaternityDailyRequiredRecordUpdateSerializer(instance, data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="更新产妇每日必填记录成功", data=MaternityDailyRequiredRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="更新产妇每日必填记录失败", data=serializer.errors)
    
    
# 删除产妇每日必填记录
class MaternityDailyRequiredRecordDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def delete(self, request, record_id):
        
        try:
            instance = MaternityDailyRequiredRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityDailyRequiredRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        instance.delete()
        return make_response(code=0, msg="删除产妇每日必填记录成功")
    
    

# 产妇入住评估详情
class MaternityCheckInAssessmentDetailView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW
    
    def get(self, request, aid):
        
        try:
            maternity_admission = MaternityAdmission.objects.get(aid=aid,maternity_center=request.user.maternity_center)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="产妇入院记录不存在")
        
        try:
            nas = MaternityCheckInAssessment.objects.get(maternity_admission=maternity_admission)
        except MaternityCheckInAssessment.DoesNotExist:
            return make_response(code=0, msg="产妇暂无入住评估记录")
        
        serializer = MaternityCheckInAssessmentDetailSerializer(nas)
        return make_response(code=0, msg="获取产妇入住评估详情成功", data=serializer.data)

# 产妇入住评估创建
class MaternityCheckInAssessmentCreateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    
    def post(self, request, aid):
        
        data = request.data.copy()
        
        try:
            maternity_admission = MaternityAdmission.objects.get(
                aid=aid,
                maternity_center=request.user.maternity_center
            )
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="产妇入院记录不存在")

        if MaternityCheckInAssessment.check_if_exists(maternity_admission.id):
            return make_response(code=-1, msg=f"当前{maternity_admission.maternity.name}（{maternity_admission.aid}）已有入住评估记录，不允许创建新的记录")
        
        data['creator'] = request.user.id
        data['maternity_admission'] = maternity_admission.id
        
        serializer = MaternityCheckInAssessmentCreateSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="创建产妇入住评估记录成功", data=MaternityCheckInAssessmentDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="创建产妇入住评估记录", data=serializer.errors)

# 产妇入住评估更新
class MaternityCheckInAssessmentUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    
    def put(self, request, aid):
        data = request.data.copy()

        try:
            nas = MaternityCheckInAssessment.objects.get(maternity_admission__aid=aid,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityCheckInAssessment.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        serializer = MaternityCheckInAssessmentUpdateSerializer(nas, data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg="更新产妇入住评估记录成功", data=MaternityCheckInAssessmentDetailSerializer(nas).data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)


# 产妇入住评估删除
class MaternityCheckInAssessmentDeleteView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT
    
    def delete(self, request, aid):
        try:
            instance = MaternityCheckInAssessment.objects.get(maternity_admission__aid=aid,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityCheckInAssessment.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        instance.delete()
        return make_response(code=0, msg="删除产妇入住评估记录成功")
    


# 产妇康复护理评估记录列表
class MaternityRehabilitationAssessmentRecordListView(MaternityRecordListBaseView):

    model_class = MaternityRehabilitationAssessmentRecord
    serializer_class = MaternityRehabilitationAssessmentRecordListSerializer
    success_message = "获取产妇康复护理评估记录列表成功"

# 产妇康复护理评估记录详情
class MaternityRehabilitationAssessmentRecordDetailView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW

    def get(self, request, record_id):
        try:
            instance = MaternityRehabilitationAssessmentRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityRehabilitationAssessmentRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        serializer = MaternityRehabilitationAssessmentRecordDetailSerializer(instance)
        return make_response(code=0, msg="获取产妇康复护理评估记录详情成功", data=serializer.data)  

# 产妇康复护理评估记录创建  
class MaternityRehabilitationAssessmentRecordCreateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def post(self, request, aid):
        
        try:
            admission = MaternityAdmission.objects.get(aid=aid,maternity_center=request.user.maternity_center)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="产妇入院记录不存在")
        
        data = request.data.copy()

        if MaternityRehabilitationAssessmentRecord.check_record_date_exists(admission.id,data['record_date']):
            return make_response(code=-1, msg=f"{admission.maternity.name}（{data['record_date']}）已有康复护理评估记录，不允许创建新的记录")

        data['creator'] = request.user.id
        data['maternity_admission'] = admission.id
        
        serializer = MaternityRehabilitationAssessmentRecordCreateSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg=f"{admission.maternity.name}（{data['record_date']}）创建康复护理评估记录成功", data=MaternityRehabilitationAssessmentRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg=f"{admission.maternity.name}（{data['record_date']}）创建康复护理评估记录失败", data=serializer.errors)
    
# 产妇康复护理评估记录更新
class MaternityRehabilitationAssessmentRecordUpdateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def put(self, request, record_id):
        try:
            instance = MaternityRehabilitationAssessmentRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityRehabilitationAssessmentRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        record_date = request.data.get('record_date')
        
        if record_date:
            if MaternityRehabilitationAssessmentRecord.check_record_date_exists(instance.maternity_admission_id, record_date,instance.id):
                return make_response(code=-1, msg=f"{instance.maternity_admission.maternity.name}（{record_date}）已有康复护理评估记录，请选择其他日期")
        
        serializer = MaternityRehabilitationAssessmentRecordUpdateSerializer(instance, data=request.data)
        if serializer.is_valid():   
            serializer.save()
            return make_response(code=0, msg=f"{instance.maternity_admission.maternity.name}（{record_date}）康复护理评估记录更新成功", data=MaternityRehabilitationAssessmentRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)
    
# 产妇康复护理评估记录删除
class MaternityRehabilitationAssessmentRecordDeleteView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def delete(self, request, record_id):
        try:
            instance = MaternityRehabilitationAssessmentRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityRehabilitationAssessmentRecord.DoesNotExist:
            return make_response(code=-1, msg="康复护理评估记录不存在")
        instance.delete()
        return make_response(code=0, msg=f"删除 {instance.maternity_admission.maternity.name}（{instance.record_date}）的康复护理评估记录成功")
    
    
    


# 产妇每日生理护理记录列表
class MaternityDailyPhysicalCareRecordListView(MaternityRecordListBaseView):

    model_class = MaternityDailyPhysicalCareRecord
    serializer_class = MaternityDailyPhysicalCareRecordListSerializer
    success_message = "获取产妇每日生理护理记录列表成功"

# 产妇每日生理护理记录详情
class MaternityDailyPhysicalCareRecordDetailView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW

    def get(self, request, record_id):
        try:
            instance = MaternityDailyPhysicalCareRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityDailyPhysicalCareRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        serializer = MaternityDailyPhysicalCareRecordDetailSerializer(instance)
        return make_response(code=0, msg="获取产妇每日生理护理记录详情成功", data=serializer.data)

# 产妇每日生理护理记录创建  
class MaternityDailyPhysicalCareRecordCreateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def post(self, request, aid):
        
        try:
            admission = MaternityAdmission.objects.get(aid=aid,maternity_center=request.user.maternity_center)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="产妇入院记录不存在")
        
        data = request.data.copy()

        if MaternityDailyPhysicalCareRecord.check_record_date_exists(admission.id,data['record_date']):
            return make_response(code=-1, msg=f"{admission.maternity.name}（{data['record_date']}）已有每日生理护理记录，不允许创建新的记录")

        data['creator'] = request.user.id
        data['maternity_admission'] = admission.id
        
        serializer = MaternityDailyPhysicalCareRecordCreateSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg=f"{admission.maternity.name}（{data['record_date']}）创建每日生理护理记录成功", data=MaternityDailyPhysicalCareRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg=f"{admission.maternity.name}（{data['record_date']}）创建每日生理护理记录失败", data=serializer.errors)
    
# 产妇每日生理护理记录更新
class MaternityDailyPhysicalCareRecordUpdateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def put(self, request, record_id):
        try:
            instance = MaternityDailyPhysicalCareRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityDailyPhysicalCareRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        record_date = request.data.get('record_date')
        
        if record_date:
            if MaternityDailyPhysicalCareRecord.check_record_date_exists(instance.maternity_admission_id, record_date,instance.id):
                return make_response(code=-1, msg=f"{instance.maternity_admission.maternity.name}（{record_date}）已有每日生理护理记录，请选择其他日期")
        
        serializer = MaternityDailyPhysicalCareRecordUpdateSerializer(instance, data=request.data)
        if serializer.is_valid():   
            serializer.save()
            return make_response(code=0, msg=f"{instance.maternity_admission.maternity.name}（{record_date}）每日生理护理记录更新成功", data=MaternityDailyPhysicalCareRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)

# 产妇每日生理护理记录删除
class MaternityDailyPhysicalCareRecordDeleteView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def delete(self, request, record_id):
        try:
            instance = MaternityDailyPhysicalCareRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityDailyPhysicalCareRecord.DoesNotExist:
            return make_response(code=-1, msg="每日生理护理记录不存在")
        instance.delete()
        return make_response(code=0, msg=f"删除 {instance.maternity_admission.maternity.name}（{instance.record_date}）的每日生理护理记录成功")
    





# 产妇膳食记录表列表
class MaternityDailyDietRecordListView(MaternityRecordListBaseView):

    model_class = MaternityDailyDietRecord
    serializer_class = MaternityDietRecordListSerializer
    success_message = "获取产妇膳食记录表列表成功"



# 产妇膳食记录表详情
class MaternityDailyDietRecordDetailView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_VIEW

    def get(self, request, record_id):
        try:
            instance = MaternityDailyDietRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityDailyDietRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        serializer = MaternityDietRecordDetailSerializer(instance)
        return make_response(code=0, msg="获取产妇膳食记录表详情成功", data=serializer.data)
        


# 产妇膳食记录表创建  
class MaternityDailyDietRecordCreateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def post(self, request, aid):
        
        try:
            admission = MaternityAdmission.objects.get(aid=aid,maternity_center=request.user.maternity_center)
        except MaternityAdmission.DoesNotExist:
            return make_response(code=-1, msg="产妇入院记录不存在")
        
        data = request.data.copy()

        if MaternityDailyDietRecord.check_record_date_exists(admission.id,data['record_date']):
            return make_response(code=-1, msg=f"{admission.maternity.name}（{data['record_date']}）已有膳食记录表，不允许创建新的记录")

        data['creator'] = request.user.id
        data['maternity_admission'] = admission.id
        
        serializer = MaternityDietRecordCreateSerializer(data=data, context={'request': request})
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0, msg=f"{admission.maternity.name}（{data['record_date']}）创建膳食记录表成功", data=MaternityDietRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg=f"{admission.maternity.name}（{data['record_date']}）创建膳食记录表失败", data=serializer.errors)
    
    
# 产妇膳食记录表更新
class MaternityDailyDietRecordUpdateView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def put(self, request, record_id):
        try:
            instance = MaternityDailyDietRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityDailyDietRecord.DoesNotExist:
            return make_response(code=-1, msg="记录不存在")
        
        record_date = request.data.get('record_date')
        
        if record_date:
            if MaternityDailyDietRecord.check_record_date_exists(instance.maternity_admission_id, record_date,instance.id):
                return make_response(code=-1, msg=f"{instance.maternity_admission.maternity.name}（{record_date}）已有膳食记录表，请选择其他日期")
        
        serializer = MaternityDietRecordUpdateSerializer(instance, data=request.data)
        
        
        if serializer.is_valid():   
            serializer.save()
            return make_response(code=0, msg=f"{instance.maternity_admission.maternity.name}（{record_date}）膳食记录表更新成功", data=MaternityDietRecordDetailSerializer(serializer.instance).data)
        return make_response(code=-1, msg="更新失败", data=serializer.errors)
    
    

# 产妇膳食记录表删除
class MaternityDailyDietRecordDeleteView(APIView):

    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.MATERNAL_CORE_RECORD_EDIT

    def delete(self, request, record_id):
        try:
            instance = MaternityDailyDietRecord.objects.get(record_id=record_id,maternity_admission__maternity_center=request.user.maternity_center)
        except MaternityDailyDietRecord.DoesNotExist:
            return make_response(code=-1, msg="膳食记录表不存在")
        instance.delete()
        return make_response(code=0, msg=f"删除 {instance.maternity_admission.maternity.name}（{instance.record_date}）的膳食记录表成功")