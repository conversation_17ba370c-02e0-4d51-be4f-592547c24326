from django.db import models

from core.generate_hashid import generate_resource_uuid
from core.model import BaseModel
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from customer_service.postpartum.enum import PostpartumProjectStatus
from maternity_center.models import MaternityCenter


# 产后康复项目
class PostpartumProject(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name='postpartum_projects')
    # 状态
    status = models.CharField(max_length=10, verbose_name="状态", help_text="产后康复项目状态",choices=PostpartumProjectStatus.choices,default=PostpartumProjectStatus.ENABLED)
    # 项目名称
    name = models.CharField(max_length=100, verbose_name="项目名称", help_text="产后康复项目名称")
    # 项目描述
    description = models.TextField(verbose_name="项目描述", help_text="产后康复项目描述")
    # rid
    rid = models.CharField(max_length=100,verbose_name="rid",help_text="产后康复项目资源 id",default=generate_resource_uuid)

    class Meta:
        verbose_name = "产后康复项目"
        verbose_name_plural = "产后康复项目"

    def __str__(self):
        return self.name
    
    @classmethod
    def get_project_by_rid(cls,rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None


# 产后康复记录
class PostpartumRecord(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心",related_name='postpartum_records')
    # 入院单
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="入院单",related_name='postpartum_records')
    # 项目
    project = models.ForeignKey(PostpartumProject, on_delete=models.CASCADE, verbose_name="项目",related_name='postpartum_records')
    # 康复时间
    recovery_time = models.DateTimeField(verbose_name="康复时间", help_text="产后康复康复时间")
    # 康复评估
    recovery_evaluation = models.TextField(verbose_name="康复评估", help_text="产后康复康复评估",blank=True,default="")
    # 康复备注
    recovery_remark = models.TextField(verbose_name="康复备注", help_text="产后康复康复备注",blank=True,default="")
    # rid
    rid = models.CharField(max_length=100,verbose_name="rid",help_text="产后康复记录资源 id",default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "产后康复记录"
        verbose_name_plural = "产后康复记录"

    def __str__(self):
        return self.project.name
    

    @classmethod
    def get_by_rid(cls,rid,maternity_center):
        try:
            return cls.objects.get(rid=rid,maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None