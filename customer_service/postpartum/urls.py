from django.urls import path
from .views import MaternitySelectListView, PostpartumProjectCreateView, PostpartumProjectListView,PostpartumProjectUpdateView, PostpartumRecordCreateView, PostpartumRecordListView

urlpatterns = [
    # 康复项目列表
    path('postpartum-project/list/', PostpartumProjectListView.as_view(), name='postpartum-project-list'),
    # 康复项目创建
    path('postpartum-project/create/', PostpartumProjectCreateView.as_view(), name='postpartum-project-create'),
    # 更新康复项目
    path('postpartum-project/update/<str:rid>/', PostpartumProjectUpdateView.as_view(), name='postpartum-project-update'),
    
    
    # 康复记录列表
    path('postpartum-record/list/', PostpartumRecordListView.as_view(), name='postpartum-record-list'),
    # 康复记录详情
    path('postpartum-record/detail/<str:rid>/', PostpartumRecordDetailView.as_view(), name='postpartum-record-detail'),
    # 康复记录创建
    path('postpartum-record/create/', PostpartumRecordCreateView.as_view(), name='postpartum-record-create'),
    
    
    
    # 在住产妇选择列表
    path('postpartum/maternity-select-list/', MaternitySelectListView.as_view(), name='postpartum-maternity-select-list'),
]