from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from core.authorization import CareCenterAuthentication, StaffWithSpecificPermissionOnly
from core.resp import make_response
from core.view import PaginationListBaseView
from customer_service.postpartum.models import PostpartumProject
from customer_service.postpartum.serializers import PostpartumProjectCreateSerializer, PostpartumProjectListSerializer, PostpartumProjectUpdateSerializer
from permissions.enum import PermissionEnum


# 康复项目列表
class PostpartumProjectListView(PaginationListBaseView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_VIEW
    serializer_class = PostpartumProjectListSerializer
    response_msg = "获取康复项目列表成功"
    error_response_msg = "获取康复项目列表失败"
    search_fields = ['name','description']
    
    def get_queryset(self):
        
        base_queryset = PostpartumProject.objects.filter(maternity_center=self.request.user.maternity_center).order_by('-created_at')
        
        
        status = self.request.query_params.get('status',None)
        
        if status:
            base_queryset.filter(status=status)

        return base_queryset
    
    

# 康复项目创建
class PostpartumProjectCreateView(APIView): 
    
    authentication_classes = [CareCenterAuthentication] 
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_EDIT
    
    def post(self,request):
        
        data = request.data.copy()
        
        data['maternity_center'] = request.user.maternity_center.id
        
        serializer = PostpartumProjectCreateSerializer(data=data)
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='康复项目创建成功',data=PostpartumProjectListSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='康复项目创建失败')
        

# 康复项目更新
class PostpartumProjectUpdateView(APIView):
    
    authentication_classes = [CareCenterAuthentication]
    permission_classes = [StaffWithSpecificPermissionOnly]
    staff_required_permission = PermissionEnum.POSTPARTUM_EDIT
    
    def put(self,request,rid):
        project = PostpartumProject.get_project_by_rid(rid,request.user.maternity_center)
        
        if not project:
            return make_response(code=-1,msg='康复项目不存在')
        
        data = request.data.copy()
                
        serializer = PostpartumProjectUpdateSerializer(project,data=data)
        
        if serializer.is_valid():
            serializer.save()
            return make_response(code=0,msg='康复项目更新成功',data=PostpartumProjectListSerializer(serializer.instance).data)
        else:
            return make_response(code=-1,msg='康复项目更新失败')