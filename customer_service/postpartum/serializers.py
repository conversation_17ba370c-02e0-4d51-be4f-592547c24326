from rest_framework import serializers

from core.parse_time import ShanghaiFriendlyDateTimeField
from customer_service.postpartum.models import PostpartumProject,PostpartumRecord

# 康复项目列表序列化器
class PostpartumProjectListSerializer(serializers.ModelSerializer):
    # 创建时间
    created_at = ShanghaiFriendlyDateTimeField()
    # 更新时间
    updated_at = ShanghaiFriendlyDateTimeField()
    # 状态标签
    status_label = serializers.SerializerMethodField()
    
    class Meta:
        model = PostpartumProject
        fields = ['rid','name','description','status','status_label','created_at','updated_at']

    def get_status_label(self,obj):
        return obj.get_status_display()

# 康复项目创建序列化器
class PostpartumProjectCreateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = PostpartumProject
        fields = ['maternity_center','name','description','status']

# 康复项目更新序列化器
class PostpartumProjectUpdateSerializer(serializers.ModelSerializer):
    
    class Meta:
        model = PostpartumProject
        fields = ['name','description','status']
        
        
        

# 康复记录列表序列化器
class PostpartumRecordListSerializer(serializers.ModelSerializer):
    
    project_name = serializers.SerializerMethodField()
    
    class Meta:
        model = PostpartumRecord
        fields = ['rid','recovery_time','recovery_evaluation','recovery_remark','project_name']
    
    def get_project_name(self,obj):
        return obj.project.name

# 康复记录详情序列化器
class PostpartumRecordDetailSerializer(serializers.ModelSerializer):
    
    project = PostpartumProjectListSerializer()
    
    class Meta:
        model = PostpartumRecord
        fields = ['rid','recovery_time','recovery_evaluation','recovery_remark','project']

        
# 康复记录创建序列化器
class PostpartumRecordCreateSerializer(serializers.ModelSerializer):
    
    project = serializers.CharField()
    
    class Meta:
        model = PostpartumRecord
        fields = ['maternity_center','maternity_admission','project','recovery_time','recovery_evaluation','recovery_remark']
        
    def validate_project(self,value):
        
        pro = PostpartumProject.get_project_by_rid(value,self.context['maternity_center'])
        
        if not pro:
            raise serializers.ValidationError("项目不存在")
        
        return pro
        

# 康复记录更新序列化器
class PostpartumRecordUpdateSerializer(serializers.ModelSerializer):
    
    project = serializers.CharField()
    
    class Meta:
        model = PostpartumRecord
        fields = ['project','recovery_time','recovery_evaluation','recovery_remark']
        
    def validate_project(self,value):
        
        pro = PostpartumProject.get_project_by_rid(value,self.context['maternity_center'])
        
        if not pro:
            raise serializers.ValidationError("项目不存在")
        
        return pro