
from django.db import models

from core.generate_hashid import generate_resource_uuid
from core.model import BaseModel
from customer_service.core_records.models.maternity_admission import MaternityAdmission
from maternity_center.models import MaternityCenter
from user.models import Maternity, Staff
from wx_mom.enum import MessageTypeEnum


# 公共健康知识模型
class PublicHealthKnowledge(BaseModel):
    # 标题
    title = models.CharField(max_length=200, verbose_name="标题")
    # 摘要
    summary = models.TextField(verbose_name="摘要")
    # 内容
    content = models.TextField(verbose_name="内容")
    # rid
    rid = models.CharField(max_length=100, verbose_name="资源标识符", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "公共健康知识"
        verbose_name_plural = "公共健康知识"
        
    def __str__(self):
        return self.title
    
    @classmethod
    def get_list(cls):
        return cls.objects.all().order_by('-created_at')
    
    @classmethod
    def get_by_rid(cls,rid):
        try:
            return cls.objects.get(rid=rid)
        except cls.DoesNotExist:
            return None


# 月子中心健康知识模型
class HealthKnowledge(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", related_name="health_knowledges")
    # 标题
    title = models.CharField(max_length=200, verbose_name="标题")
    # 摘要
    summary = models.TextField(verbose_name="摘要")
    # 内容
    content = models.TextField(verbose_name="内容")
    # rid
    rid = models.CharField(max_length=100, verbose_name="资源标识符", default=generate_resource_uuid)
    
    class Meta:
        verbose_name = "健康知识"
        verbose_name_plural = "健康知识"
        
    def __str__(self):
        return self.title
    
    
    @classmethod
    def get_list(cls,cid):
        return cls.objects.filter(maternity_center__cid=cid).order_by('-created_at')
    
    @classmethod
    def get_by_rid(cls,rid):
        print('开始搜索：'+rid)
        try:
            return cls.objects.get(rid=rid)
        except cls.DoesNotExist:
            return None




# 产检提醒模型
class PrenatalCheckReminder(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", related_name="prenatal_check_reminders")
    # 产妇
    maternity = models.ForeignKey(Maternity, on_delete=models.CASCADE, verbose_name="产妇", related_name="prenatal_check_reminders")
    # 孕周
    week = models.PositiveIntegerField(verbose_name="孕周")
    # 孕周范围
    week_range = models.CharField(max_length=50, verbose_name="孕周范围")
    # 检查名称
    name = models.CharField(max_length=200, verbose_name="检查名称")
    # 检查描述
    description = models.TextField(verbose_name="检查描述")
    # 关键检查项目
    key_items = models.JSONField(verbose_name="关键检查项目", default=list)
    # 注意事项
    notes = models.TextField(verbose_name="注意事项", blank=True, default="")
    # 检查日期
    check_date = models.DateField(verbose_name="检查日期")
    # 是否为重要检查
    is_important = models.BooleanField(verbose_name="是否为重要检查", default=False)
    # 检查标签
    tags = models.JSONField(verbose_name="检查标签", default=list)
    # 建议检查时间
    suggested_time = models.CharField(max_length=50, verbose_name="建议检查时间", blank=True, default="")
    # 是否已完成
    is_completed = models.BooleanField(verbose_name="是否已完成", default=False)
    # rid
    rid = models.CharField(max_length=100, verbose_name="标识符", default=generate_resource_uuid)

    class Meta:
        verbose_name = "产检提醒"
        verbose_name_plural = "产检提醒"
        ordering = ['check_date', 'week']
        indexes = [
            models.Index(fields=['maternity_center', 'maternity']),
            models.Index(fields=['check_date']),
            models.Index(fields=['week']),
            models.Index(fields=['is_completed']),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['maternity', 'week'],
                name='unique_maternity_week_prenatal_check'
            )
        ]

    def __str__(self):
        return f"{self.maternity.name} - {self.name} ({self.week}周)"

    @classmethod
    def get_prenatal_check_by_rid(cls, rid, maternity_center):
        try:
            return cls.objects.get(rid=rid, maternity_center=maternity_center)
        except cls.DoesNotExist:
            return None

    @classmethod
    def get_maternity_prenatal_checks(cls, maternity, completed=None):
        
        queryset = cls.objects.filter(maternity=maternity)

        if completed is not None:
            queryset = queryset.filter(is_completed=completed)

        return queryset.order_by('check_date', 'week')

    def mark_completed(self):
        self.is_completed = True
        self.save()


# 未付费用户产妇信息表
class UnpaidMaternityPrenatalInfo(BaseModel):
    
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", related_name="unpaid_maternity_prenatal_infos")
    # 产妇
    maternity = models.ForeignKey(Maternity, on_delete=models.CASCADE, verbose_name="产妇", related_name="unpaid_maternity_prenatal_infos")
    # 预计分娩日期
    expected_delivery_date = models.DateField(verbose_name="预计分娩日期")
    # rid
    rid = models.CharField(max_length=100, verbose_name="标识符", default=generate_resource_uuid)

    class Meta:
        verbose_name = "未付费用户产妇信息"
        verbose_name_plural = "未付费用户产妇信息"
        
    def __str__(self):
        return f"{self.maternity.name} - {self.expected_delivery_date})"
    
    def update_expected_delivery_date(self, expected_delivery_date):
        self.expected_delivery_date = expected_delivery_date
        self.save()
    
    
    @classmethod
    def create_unpaid_maternity_prenatal_info(cls, user, expected_delivery_date):
        return cls.objects.create(maternity=user, maternity_center=user.maternity_center, expected_delivery_date=expected_delivery_date)



    @classmethod
    def get_unpaid_maternity_prenatal_info(cls, user):
        try:
            return cls.objects.get(maternity=user)
        except cls.DoesNotExist:
            return None
        
        
        
# 消息中心
class WxMaternityMessage(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", related_name="wx_maternity_messages")
    # 入院单
    maternity_admission = models.ForeignKey(MaternityAdmission, on_delete=models.CASCADE, verbose_name="入院单", related_name="wx_maternity_messages")
    # 消息类型
    message_type = models.CharField(max_length=100, verbose_name="消息类型", choices=MessageTypeEnum.choices)
    # 消息内容
    message_content = models.TextField(verbose_name="消息内容")
    # 是否已读
    is_read = models.BooleanField(verbose_name="是否已读", default=False)
    # 是否被获取过
    is_loaded = models.BooleanField(verbose_name="是否被轮询过", default=False)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.SET_NULL, verbose_name="创建人", related_name="creator_wx_maternity_messages", null=True, blank=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="标识符", default=generate_resource_uuid)
    
    
    class Meta:
        verbose_name = "消息中心"
        verbose_name_plural = "消息中心"
        
    def __str__(self):
        maternity_name = self.maternity_admission.maternity.name if self.maternity_admission and self.maternity_admission.maternity else "未知产妇"
        return f"{maternity_name} - {self.get_message_type_display()}"
    
    def mark_message_as_read(self):
        self.is_read = True
        self.save()
    
    @staticmethod
    def send_message(maternity, message_type, message_content):
        message = WxMaternityMessage.objects.create(maternity_center=maternity.maternity_center, maternity=maternity, message_type=message_type, message_content=message_content)
        message.save()
        return message
    
    
    @staticmethod
    def get_message_by_rid(rid, user):
        
        try:
            return WxMaternityMessage.objects.get(rid=rid, maternity_admission__maternity=user)
        except WxMaternityMessage.DoesNotExist:
            return None
        
        
    @staticmethod
    def get_messages_by_maternity_center(maternity_center):
        return WxMaternityMessage.objects.filter(maternity_center=maternity_center)
    
    

    
    
    
# 未付费会员消息中心
class WxUnpaidMaternityMessage(BaseModel):
    # 月子中心
    maternity_center = models.ForeignKey(MaternityCenter, on_delete=models.CASCADE, verbose_name="月子中心", related_name="wx_unpaid_maternity_messages")
    # 产妇
    maternity = models.ForeignKey(Maternity, on_delete=models.CASCADE, verbose_name="产妇", related_name="wx_unpaid_maternity_messages")
    # 消息类型
    message_type = models.CharField(max_length=100, verbose_name="消息类型", choices=MessageTypeEnum.choices)
    # 消息内容
    message_content = models.TextField(verbose_name="消息内容")
    # 是否已读
    is_read = models.BooleanField(verbose_name="是否已读", default=False)
    # 是否被获取过
    is_loaded = models.BooleanField(verbose_name="是否被轮询过", default=False)
    # 创建人
    creator = models.ForeignKey(Staff, on_delete=models.SET_NULL, verbose_name="创建人", related_name="creator_wx_unpaid_maternity_messages", null=True, blank=True)
    # rid
    rid = models.CharField(max_length=100, verbose_name="标识符", default=generate_resource_uuid)
    
    
    class Meta:
        verbose_name = "未付费会员消息中心"
        verbose_name_plural = "未付费会员消息中心"
        
    def __str__(self):
        maternity_name = self.maternity.name if self.maternity else "未知产妇"
        return f"{maternity_name} - {self.get_message_type_display()}"
    
    def mark_message_as_read(self):
        self.is_read = True
        self.save()
    
    @staticmethod
    def send_message(maternity, message_type, message_content):
        message = WxUnpaidMaternityMessage.objects.create(maternity_center=maternity.maternity_center, maternity=maternity, message_type=message_type, message_content=message_content)
        message.save()
        return message
    
    
    @staticmethod
    def get_message_by_rid(rid, user):
        
        try:
            return WxUnpaidMaternityMessage.objects.get(rid=rid, maternity=user)
        except WxUnpaidMaternityMessage.DoesNotExist:
            return None
        
        
    @staticmethod
    def get_messages_by_maternity_center(maternity_center):
        return WxUnpaidMaternityMessage.objects.filter(maternity_center=maternity_center)
    
    

    